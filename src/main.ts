// 引入unocss css
import '@/plugins/unocss'

// 导入全局的svg图标
import '@/plugins/svgIcon'

// 初始化多语言
import { setupI18n } from '@/plugins/vueI18n'

// 引入状态管理
import { setupStore } from '@/store'

// 全局组件
import { setupGlobCom } from '@/components'

// 引入 element-plus
import { setupElementPlus } from '@/plugins/elementPlus'

// 引入 form-create
import { setupFormCreate } from '@/plugins/formCreate'

// 引入全局样式
import '@/styles/index.scss'

// 引入动画
import '@/plugins/animate.css'

// 路由
import router, { setupRouter } from '@/router'

// 指令
import { setupAuth, setupMountedFocus } from '@/directives'

import { createApp } from 'vue'

import App from './App.vue'

import './permission'

import '@/plugins/tongji' // 百度统计
import Logger from '@/utils/Logger'

import VueDOMPurifyHTML from 'vue-dompurify-html' // 解决v-html 的安全隐患

// iframe 嵌入检测和通信设置
function setupIframeIntegration() {
  // 检测是否在 iframe 中
  if (window.self !== window.top) {
    // 添加 iframe 嵌入标识
    document.body.classList.add('iframe-embedded')
    document.getElementById('app')?.classList.add('iframe-embedded')

    // 监听来自父窗口的消息
    window.addEventListener('message', (event) => {
      const { type, data } = event.data

      switch (type) {
        case 'SET_USER_TOKEN':
          // 设置用户认证信息
          if (data?.token) {
            localStorage.setItem('ACCESS_TOKEN', data.token)
          }
          if (data?.tenantId) {
            localStorage.setItem('TENANT_ID', data.tenantId)
          }
          break
        case 'NAVIGATE_TO':
          // 路由导航
          if (data?.path && router) {
            router.push(data.path)
          }
          break
        default:
          break
      }
    })

    // 向父窗口发送就绪消息
    if (window.parent) {
      window.parent.postMessage(
        {
          type: 'BPM_MODULE_READY',
          data: { timestamp: Date.now() }
        },
        '*'
      )
    }
  }
}

// 向父窗口发送消息的工具函数
function sendMessageToParent(type: string, data?: any) {
  if (window.self !== window.top && window.parent) {
    window.parent.postMessage({ type, data }, '*')
  }
}

// 全局暴露通信方法
;(window as any).sendMessageToParent = sendMessageToParent

// 创建实例
const setupAll = async () => {
  const app = createApp(App)

  await setupI18n(app)

  setupStore(app)

  setupGlobCom(app)

  setupElementPlus(app)

  setupFormCreate(app)

  setupRouter(app)

  // directives 指令
  setupAuth(app)
  setupMountedFocus(app)

  await router.isReady()

  app.use(VueDOMPurifyHTML)

  app.mount('#app')

  // 设置 iframe 集成
  setupIframeIntegration()
}

setupAll()

Logger.prettyPrimary(`欢迎使用`, import.meta.env.VITE_APP_TITLE)
